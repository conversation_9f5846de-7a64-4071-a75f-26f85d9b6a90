import React from "react";
import { cn } from "@/lib/utils";

interface CategoryIconProps {
  icon: string;
  title: string;
  image?: string;
  className?: string;
  onClick?: () => void;
}

export const CategoryIcon: React.FC<CategoryIconProps> = ({
  icon,
  title,
  image,
  className,
  onClick,
}) => {
  return (
    <div
      className={cn(
        "flex flex-col items-center gap-2 p-2 cursor-pointer",
        "hover:bg-muted/50 rounded-lg transition-colors",
        className
      )}
      onClick={onClick}
    >
      <div className="w-14 h-14 bg-mobile-category-icon rounded-xl flex items-center justify-center shadow-sm border border-border/50 overflow-hidden">
        {image ? (
          <img
            src={image}
            alt={title}
            className="w-full h-full object-cover"
            onError={(e) => {
              // Fallback to icon if image fails to load
              e.currentTarget.style.display = "none";
              e.currentTarget.nextElementSibling?.classList.remove("hidden");
            }}
          />
        ) : null}
        <span className={cn("text-2xl", image ? "hidden" : "")}>{icon}</span>
      </div>
      <span className="text-xs text-foreground font-medium text-center leading-tight">
        {title}
      </span>
    </div>
  );
};
