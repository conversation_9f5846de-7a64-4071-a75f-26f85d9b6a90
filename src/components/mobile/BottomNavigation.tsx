import React from "react";
import { cn } from "@/lib/utils";
import {
  Home,
  Grid3X3,
  FileText,
  User,
  Search,
  ShoppingCart,
} from "lucide-react";

// Icon mapping for different icon types
const iconMap = {
  iconly: {
    Home: Home,
    Category: Grid3X3,
    Paper: FileText,
    User: User,
    Search: Search,
    Cart: ShoppingCart,
  },
};

interface NavItem {
  id?: string;
  icon: string;
  label: string;
  active?: boolean;
  href?: string;
  type?: string;
  iconActive?: string;
  typeActive?: string;
}

interface MenuBottomConfig {
  visible: boolean;
  layouts: NavItem[];
  settings: {
    margin?: string;
    padding?: string;
    background?: string;
    border?: string;
    borderRadius?: string;
    boxShadow?: string;
    cardStyle?: {
      inactiveColor?: string;
      activeColor?: string;
      activeBackground?: string;
      sizeIcon?: number;
      visibleLabel?: boolean;
      fontSizeLabel?: number;
    };
  };
}

interface BottomNavigationProps {
  items?: NavItem[];
  config?: MenuBottomConfig;
  onItemClick?: (id: string, href?: string) => void;
  className?: string;
  activeItemId?: string;
}

// Helper function to get icon component
const getIconComponent = (iconName: string, type: string = "emoji") => {
  if (
    type === "iconly" &&
    iconMap.iconly[iconName as keyof typeof iconMap.iconly]
  ) {
    return iconMap.iconly[iconName as keyof typeof iconMap.iconly];
  }
  return null;
};

export const BottomNavigation: React.FC<BottomNavigationProps> = ({
  items,
  config,
  onItemClick,
  className,
  activeItemId,
}) => {
  // Use config data if provided, otherwise fall back to items
  const navigationItems = config?.layouts || items || [];
  const settings = config?.settings;

  if (config && !config.visible) {
    return null;
  }

  // Generate dynamic styles from settings
  const containerStyle = settings
    ? {
        margin: settings.margin || "0px",
        padding: settings.padding || "0px",
        background: settings.background || "rgba(255, 255, 255, 1)",
        border: settings.border || "0px black solid",
        borderRadius: settings.borderRadius || "0px",
        boxShadow: settings.boxShadow || "",
      }
    : {};

  return (
    <div
      className={cn(
        "flex items-center justify-around py-2 px-4",
        "bg-mobile-header border-t border-border/50",
        "absolute bottom-0 left-0 right-0 z-20",
        className
      )}
      style={containerStyle}
    >
      {navigationItems.map((item, index) => {
        const itemId = item.id || item.href || `item-${index}`;
        const isActive = activeItemId ? activeItemId === itemId : item.active;

        // Determine which icon to use
        const iconToUse =
          isActive && item.iconActive ? item.iconActive : item.icon;
        const iconType =
          isActive && item.typeActive ? item.typeActive : item.type;

        const IconComponent = getIconComponent(iconToUse, iconType);

        // Apply card style settings
        const cardStyle = settings?.cardStyle;
        const iconSize = cardStyle?.sizeIcon || 24;
        const showLabel = cardStyle?.visibleLabel !== false;
        const labelFontSize = cardStyle?.fontSizeLabel || 12;

        const buttonStyle = cardStyle
          ? {
              color: isActive ? cardStyle.activeColor : cardStyle.inactiveColor,
              backgroundColor: isActive
                ? cardStyle.activeBackground
                : "transparent",
            }
          : {};

        return (
          <button
            key={itemId}
            onClick={() => onItemClick?.(itemId, item.href)}
            className={cn(
              "flex flex-col items-center gap-1 p-2 rounded-lg",
              "transition-colors duration-200",
              !cardStyle &&
                (isActive
                  ? "text-primary"
                  : "text-muted-foreground hover:text-foreground")
            )}
            style={buttonStyle}
          >
            {IconComponent ? (
              <IconComponent size={iconSize} />
            ) : (
              <span style={{ fontSize: `${iconSize}px` }}>{iconToUse}</span>
            )}
            {showLabel && (
              <span
                className="font-medium"
                style={{ fontSize: `${labelFontSize}px` }}
              >
                {item.label}
              </span>
            )}
          </button>
        );
      })}
    </div>
  );
};
