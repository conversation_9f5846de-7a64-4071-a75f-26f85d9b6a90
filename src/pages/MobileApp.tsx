import { useState, useEffect } from "react";
import { DragDropContext, Droppable, DropResult } from "react-beautiful-dnd";
import { MobileScreen } from "@/components/mobile/MobileScreen";
import { DEVICE_MODELS, DeviceModel } from "@/components/mobile/DeviceSelector";
import { CategoryIcon } from "@/components/mobile/CategoryIcon";
import { FlashSale } from "@/components/mobile/FlashSale";
import { ProductCard } from "@/components/mobile/ProductCard";
import { BannerCard } from "@/components/mobile/BannerCard";
import { DraggableSection } from "@/components/mobile/DraggableSection";
import { BottomNavigation } from "@/components/mobile/BottomNavigation";
import { useToast } from "@/hooks/use-toast";
import appData from "./data.json";

interface AppSection {
  id: string;
  type: string;
  content: any;
  settings?: any;
}

const MobileApp = () => {
  const { toast } = useToast();

  const [selectedPlatform, setSelectedPlatform] = useState<
    "iphone" | "android"
  >("iphone");
  const [selectedModel, setSelectedModel] = useState<string>("iphone-15-pro");
  const [sections, setSections] = useState<AppSection[]>([]);
  const [bottomNavItems, setBottomNavItems] = useState<any[]>([]);

  const getCurrentDevice = (): DeviceModel => {
    const models = DEVICE_MODELS[selectedPlatform];
    return models.find((model) => model.id === selectedModel) || models[0];
  };

  // Load data from JSON file
  useEffect(() => {
    const loadAppData = () => {
      const homePageData = appData.page["/home"];
      const loadedSections: AppSection[] = [];

      // Load layouts from home page
      if (homePageData && homePageData.layouts) {
        Object.entries(homePageData.layouts).forEach(
          ([layoutKey, layoutData]: [string, any]) => {
            if (layoutData.visible) {
              switch (layoutKey) {
                case "Header1":
                  // Header component - we can add this later
                  break;
                case "Category2":
                  loadedSections.push({
                    id: `category-${layoutKey}`,
                    type: "categories",
                    content: {
                      items:
                        layoutData.data?.map((item: any) => ({
                          icon: "📱", // Default icon since we don't have emoji in the data
                          title: item.name,
                          image: item.image_url,
                          url: item.url,
                        })) || [],
                    },
                    settings: layoutData.settings,
                  });
                  break;
                default:
                  break;
              }
            }
          }
        );
      }

      // Add some default sections for demo
      loadedSections.push(
        {
          id: "flash-sale",
          type: "flash-sale",
          content: {},
        },
        {
          id: "products-1",
          type: "products",
          content: {
            items: [
              {
                id: "p1",
                image:
                  "/lovable-uploads/c8090615-afb2-4186-9fcb-b5453f8d73a4.png",
                title: "Sữa bột Nestilac Pedia Ion 800g",
                price: "485.000đ",
                originalPrice: "520.000đ",
                discount: "Giảm 5%",
                rating: 5,
                sold: 0,
              },
              {
                id: "p2",
                image:
                  "/lovable-uploads/c8090615-afb2-4186-9fcb-b5453f8d73a4.png",
                title: "Quần áo trẻ em mới",
                price: "140.000đ",
                originalPrice: "180.000đ",
                discount: "Giảm 22%",
                rating: 5,
                sold: 0,
              },
            ],
          },
        }
      );

      setSections(loadedSections);

      // Load bottom navigation
      const menuData = appData.menuBottom?.Menu1;
      if (menuData && menuData.visible) {
        const navItems = menuData.layouts.map((item: any, index: number) => ({
          id: item.href.replace("/", "") || `nav-${index}`,
          icon: getIconEmoji(item.icon),
          label: item.label,
          active: item.href === "/home",
          href: item.href,
        }));
        setBottomNavItems(navItems);
      }
    };

    loadAppData();
  }, []);

  // Helper function to convert icon names to emojis
  const getIconEmoji = (iconName: string): string => {
    const iconMap: { [key: string]: string } = {
      Home: "🏠",
      Category: "📂",
      Paper: "📰",
      User: "👤",
      Search: "🔍",
      ShoppingCart: "🛒",
    };
    return iconMap[iconName] || "📱";
  };

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const items = Array.from(sections);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    setSections(items);

    toast({
      title: "Section đã được di chuyển!",
      description: `${result.draggableId} đã được di chuyển từ vị trí ${
        result.source.index + 1
      } sang vị trí ${result.destination.index + 1}`,
    });
  };

  const renderSection = (section: AppSection, index: number) => {
    switch (section.type) {
      case "categories":
        const colCount = section.settings?.col || 4;
        const gridCols =
          colCount === 4
            ? "grid-cols-4"
            : colCount === 5
            ? "grid-cols-5"
            : "grid-cols-4";

        return (
          <DraggableSection key={section.id} id={section.id} index={index}>
            <div className={`grid ${gridCols} gap-2 px-4 mb-4`}>
              {section.content.items.map((item: any, idx: number) => (
                <CategoryIcon
                  key={idx}
                  icon={item.icon || "📱"}
                  title={item.title}
                  image={item.image}
                />
              ))}
            </div>
          </DraggableSection>
        );

      case "flash-sale":
        return (
          <DraggableSection key={section.id} id={section.id} index={index}>
            <FlashSale className="mb-4" />
          </DraggableSection>
        );

      case "products":
        return (
          <DraggableSection key={section.id} id={section.id} index={index}>
            <div className="px-4 mb-4">
              <div className="grid grid-cols-3 gap-3">
                {section.content.items.map((product: any) => (
                  <ProductCard
                    key={product.id}
                    image={product.image}
                    title={product.title}
                    price={product.price}
                    originalPrice={product.originalPrice}
                    discount={product.discount}
                    rating={product.rating}
                    sold={product.sold}
                  />
                ))}
              </div>
            </div>
          </DraggableSection>
        );

      case "banners":
        return (
          <DraggableSection key={section.id} id={section.id} index={index}>
            <div className="px-4 mb-4 space-y-3">
              {section.content.items.map((banner: any) => (
                <BannerCard
                  key={banner.id}
                  title={banner.title}
                  subtitle={banner.subtitle}
                  image={banner.image}
                  backgroundColor={banner.backgroundColor}
                />
              ))}
            </div>
          </DraggableSection>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-muted/30 ">
      <div className="max-w-6xl mx-auto">
        {/* <DeviceSelector
          selectedPlatform={selectedPlatform}
          selectedModel={selectedModel}
          onPlatformChange={handlePlatformChange}
          onModelChange={setSelectedModel}
        /> */}

        <div className="flex justify-center">
          <MobileScreen deviceModel={getCurrentDevice()}>
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="mobile-app">
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className="h-full overflow-y-auto pb-16 scrollbar-hide"
                    style={{
                      scrollbarWidth: "none",
                      msOverflowStyle: "none",
                    }}
                  >
                    {/* Scrollable Content */}
                    <div className="pt-4">
                      {sections.map((section, index) =>
                        renderSection(section, index)
                      )}
                      {provided.placeholder}
                    </div>
                  </div>
                )}
              </Droppable>
            </DragDropContext>

            {/* Bottom Navigation */}
            <BottomNavigation
              items={bottomNavItems}
              onItemClick={(id) => console.log("Nav clicked:", id)}
            />
          </MobileScreen>
        </div>
      </div>
    </div>
  );
};

export default MobileApp;
