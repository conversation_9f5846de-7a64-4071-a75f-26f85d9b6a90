import { useState, useEffect } from "react";
import { DragDropContext, Droppable, DropResult } from "react-beautiful-dnd";
import { MobileScreen } from "@/components/mobile/MobileScreen";
import { DEVICE_MODELS, DeviceModel } from "@/components/mobile/DeviceSelector";
import { CategoryIcon } from "@/components/mobile/CategoryIcon";
import { FlashSale } from "@/components/mobile/FlashSale";
import { ProductCard } from "@/components/mobile/ProductCard";
import { BannerCard } from "@/components/mobile/BannerCard";
import { DraggableSection } from "@/components/mobile/DraggableSection";
import { BottomNavigation } from "@/components/mobile/BottomNavigation";
import { useToast } from "@/hooks/use-toast";
import appData from "./data.json";
import {
  Home,
  LayoutGrid,
  Search,
  User,
  ShoppingCart,
  FileText,
  ShoppingBag,
  Filter,
  MessageCircle,
  Settings,
  Gift,
  Package,
  Smartphone,
  Baby,
  Gamepad2,
  Palette,
  Apple,
  Car,
  Shirt,
  <PERSON>,
  Monitor,
  Archive,
} from "lucide-react";

interface AppSection {
  id: string;
  type: string;
  content: any;
  settings?: any;
}

interface PageData {
  name: string;
  icon: string;
  href: string;
  layouts: any;
}

const MobileApp = () => {
  const { toast } = useToast();

  const [selectedPlatform, setSelectedPlatform] = useState<
    "iphone" | "android"
  >("iphone");
  const [selectedModel, setSelectedModel] = useState<string>("iphone-15-pro");
  const [sections, setSections] = useState<AppSection[]>([]);
  const [bottomNavItems, setBottomNavItems] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState<string>("/home");

  const getCurrentDevice = (): DeviceModel => {
    const models = DEVICE_MODELS[selectedPlatform];
    return models.find((model) => model.id === selectedModel) || models[0];
  };

  // Load data from JSON file based on current page
  const loadPageData = (pagePath: string) => {
    const pageData = appData.page[pagePath];
    const loadedSections: AppSection[] = [];

    if (pageData && pageData.layouts) {
      Object.entries(pageData.layouts).forEach(
        ([layoutKey, layoutData]: [string, any]) => {
          if (layoutData.visible) {
            switch (layoutKey) {
              case "Header1":
                loadedSections.push({
                  id: `header-${layoutKey}`,
                  type: "header",
                  content: layoutData.settings,
                  settings: layoutData.settings,
                });
                break;
              case "Category2":
                loadedSections.push({
                  id: `category-${layoutKey}`,
                  type: "categories",
                  content: {
                    items:
                      layoutData.data?.map((item: any) => ({
                        icon: Smartphone, // Use Lucide icon instead of emoji
                        title: item.name,
                        image: item.image_url,
                        url: item.url,
                      })) || [],
                  },
                  settings: layoutData.settings,
                });
                break;
              case "PageCategory1":
                // Category page layout
                if (
                  layoutData.defaultData &&
                  layoutData.defaultData.length > 0
                ) {
                  loadedSections.push({
                    id: `category-list`,
                    type: "category-list",
                    content: {
                      items: layoutData.defaultData.map((item: any) => ({
                        id: item.xid,
                        name: item.name,
                        image: item.image_url,
                        banner: item.banner_image_url,
                        slug: item.slug,
                        childs: item.childs || [],
                      })),
                    },
                    settings: layoutData.settings,
                  });
                }
                break;
              case "PageSearch1":
                // Search page layout
                loadedSections.push({
                  id: `search-page`,
                  type: "search",
                  content: {},
                  settings: layoutData.settings,
                });
                break;
              case "PageProfile1":
                // Profile page layout
                loadedSections.push({
                  id: `profile-page`,
                  type: "profile",
                  content: {},
                  settings: layoutData.settings,
                });
                break;
              case "PageProductDetail1":
                // Product detail page
                if (layoutData.data) {
                  const productData = layoutData.data;
                  loadedSections.push({
                    id: `product-detail`,
                    type: "product-detail",
                    content: {
                      product: {
                        id: productData.xid,
                        name: productData.name,
                        image: productData.image_url,
                        price: productData.details?.sales_price,
                        originalPrice: productData.details?.mrp,
                        description: productData.description,
                        category: productData.category?.name,
                        brand: productData.brand?.name,
                        stock: productData.details?.current_stock,
                        rating: productData.ratings || [],
                        sold: productData.total_sold,
                      },
                    },
                    settings: layoutData.settings,
                  });
                }
                break;
              case "PageCart1":
                // Cart page layout
                loadedSections.push({
                  id: `cart-page`,
                  type: "cart",
                  content: {},
                  settings: layoutData.settings,
                });
                break;
              default:
                break;
            }
          }
        }
      );
    }

    setSections(loadedSections);
  };

  useEffect(() => {
    loadPageData(currentPage);

    // Load bottom navigation
    const menuData = appData.menuBottom?.Menu1;
    if (menuData && menuData.visible) {
      const navItems = menuData.layouts.map((item: any, index: number) => ({
        id: item.href.replace("/", "") || `nav-${index}`,
        icon: getLucideIcon(item.icon),
        label: item.label,
        active: item.href === currentPage,
        href: item.href,
      }));
      setBottomNavItems(navItems);
    }
  }, [currentPage]);

  // Helper function to convert icon names to Lucide icons
  const getLucideIcon = (iconName: string) => {
    const iconMap: { [key: string]: any } = {
      Home: Home,
      Category: LayoutGrid,
      Paper: FileText,
      User: User,
      Search: Search,
      ShoppingCart: ShoppingCart,
      LayoutGrid: LayoutGrid,
      Cuboid: ShoppingBag,
    };
    return iconMap[iconName] || ShoppingBag;
  };

  // Helper function to convert emoji to Lucide icons
  const getIconFromEmoji = (emoji: string) => {
    const emojiToIconMap: { [key: string]: any } = {
      "🍼": Milk,
      "👶": Baby,
      "🧸": Gamepad2,
      "💄": Palette,
      "🍎": Apple,
      "🚗": Car,
      "👕": Shirt,
      "📱": Smartphone,
      "📦": Archive,
      "🛒": ShoppingCart,
      "🔍": Search,
      "👤": User,
      "🏠": Home,
      "📂": LayoutGrid,
      "📰": FileText,
    };
    return emojiToIconMap[emoji] || ShoppingBag;
  };

  // Handle navigation
  const handleNavigation = (href: string) => {
    setCurrentPage(href);
    // Update active state for bottom nav
    setBottomNavItems((prev) =>
      prev.map((item) => ({
        ...item,
        active: item.href === href,
      }))
    );
  };

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const items = Array.from(sections);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    setSections(items);
  };

  const renderSection = (section: AppSection, index: number) => {
    switch (section.type) {
      case "header":
        return (
          <DraggableSection key={section.id} id={section.id} index={index}>
            <div className="px-4 py-2 bg-white">
              <div className="flex items-center justify-between">
                <div>
                  {section.content.visibleTitle && (
                    <h1
                      className="text-lg font-semibold"
                      style={{ color: section.content.colorTitle }}
                    >
                      {section.content.title}
                    </h1>
                  )}
                  {section.content.visibleSubTitle && (
                    <p
                      className="text-sm"
                      style={{ color: section.content.colorSubTitle }}
                    >
                      {section.content.subTitle}
                    </p>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {section.content.visibleCartIcon && (
                    <ShoppingCart size={20} />
                  )}
                  {section.content.visibleFilterIcon && <Filter size={20} />}
                  {section.content.visibleMessageIcon && (
                    <MessageCircle size={20} />
                  )}
                </div>
              </div>
              {section.content.visibleSearchBar && (
                <div className="mt-2">
                  <input
                    type="text"
                    placeholder={section.content.placeholderSearchBar}
                    className="w-full px-3 py-2 border rounded-lg"
                  />
                </div>
              )}
            </div>
          </DraggableSection>
        );

      case "categories":
        const colCount = section.settings?.col || 4;
        const gridCols =
          colCount === 4
            ? "grid-cols-4"
            : colCount === 5
            ? "grid-cols-5"
            : "grid-cols-4";

        return (
          <DraggableSection key={section.id} id={section.id} index={index}>
            <div className={`grid ${gridCols} gap-2 px-4 mb-4`}>
              {section.content.items.map((item: any, idx: number) => (
                <CategoryIcon
                  key={idx}
                  icon={item.icon || ShoppingBag}
                  title={item.title}
                  image={item.image}
                  onClick={() => item.url && handleNavigation(item.url)}
                />
              ))}

              {/* Demo section with Lucide icons for common categories */}
              {section.id === "category-Category2" && (
                <>
                  <CategoryIcon icon={Milk} title="Sữa" />
                  <CategoryIcon icon={Baby} title="Bỉm tã" />
                  <CategoryIcon icon={Gamepad2} title="Đồ chơi" />
                  <CategoryIcon icon={Palette} title="Mỹ phẩm" />
                  <CategoryIcon icon={Apple} title="Thực phẩm" />
                  <CategoryIcon icon={Car} title="Xe đẩy" />
                  <CategoryIcon icon={Shirt} title="Quần áo" />
                  <CategoryIcon icon={Milk} title="Bình sữa" />
                  <CategoryIcon icon={Smartphone} title="Thiết bị" />
                  <CategoryIcon icon={Archive} title="Tất cả" />
                </>
              )}
            </div>
          </DraggableSection>
        );

      case "category-list":
        return (
          <DraggableSection key={section.id} id={section.id} index={index}>
            <div className="px-4 mb-4">
              <div className="grid grid-cols-2 gap-3">
                {section.content.items.map((category: any) => (
                  <div
                    key={category.id}
                    className="bg-white rounded-lg p-3 shadow-sm border"
                  >
                    <img
                      src={category.image}
                      alt={category.name}
                      className="w-full h-20 object-cover rounded mb-2"
                    />
                    <h3 className="font-medium text-sm">{category.name}</h3>
                    {category.childs && category.childs.length > 0 && (
                      <p className="text-xs text-gray-500">
                        {category.childs.length} sản phẩm
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </DraggableSection>
        );

      case "search":
        return (
          <DraggableSection key={section.id} id={section.id} index={index}>
            <div className="px-4 mb-4">
              <div className="bg-white rounded-lg p-4">
                <input
                  type="text"
                  placeholder="Tìm kiếm sản phẩm..."
                  className="w-full px-3 py-2 border rounded-lg mb-4"
                />
                <div className="text-center text-gray-500">
                  <p>Nhập từ khóa để tìm kiếm sản phẩm</p>
                </div>
              </div>
            </div>
          </DraggableSection>
        );

      case "profile":
        return (
          <DraggableSection key={section.id} id={section.id} index={index}>
            <div className="px-4 mb-4">
              <div className="bg-white rounded-lg p-4">
                <div className="flex items-center mb-4">
                  <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                    <User size={32} className="text-gray-600" />
                  </div>
                  <div className="ml-3">
                    <h3 className="font-semibold">Người dùng</h3>
                    <p className="text-sm text-gray-500">
                      Chào mừng bạn đến với ứng dụng
                    </p>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between py-2">
                    <span>Đơn hàng của tôi</span>
                    <Package size={20} className="text-gray-600" />
                  </div>
                  <div className="flex items-center justify-between py-2">
                    <span>Ưu đãi của tôi</span>
                    <Gift size={20} className="text-gray-600" />
                  </div>
                  <div className="flex items-center justify-between py-2">
                    <span>Cài đặt</span>
                    <Settings size={20} className="text-gray-600" />
                  </div>
                </div>
              </div>
            </div>
          </DraggableSection>
        );

      case "cart":
        return (
          <DraggableSection key={section.id} id={section.id} index={index}>
            <div className="px-4 mb-4">
              <div className="bg-white rounded-lg p-4">
                <div className="text-center text-gray-500">
                  <ShoppingCart size={48} className="mx-auto text-gray-400" />
                  <p className="mt-2">Giỏ hàng trống</p>
                  <p className="text-sm">Hãy thêm sản phẩm vào giỏ hàng</p>
                </div>
              </div>
            </div>
          </DraggableSection>
        );

      case "product-detail":
        const product = section.content.product;
        return (
          <DraggableSection key={section.id} id={section.id} index={index}>
            <div className="px-4 mb-4">
              <div className="bg-white rounded-lg p-4">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-48 object-cover rounded mb-3"
                />
                <h3 className="font-semibold text-lg mb-2">{product.name}</h3>
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-lg font-bold text-red-500">
                    {product.price?.toLocaleString()}đ
                  </span>
                  {product.originalPrice && (
                    <span className="text-sm text-gray-500 line-through">
                      {product.originalPrice?.toLocaleString()}đ
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-600 mb-3">{product.category}</p>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Đã bán: {product.sold}</span>
                  <span className="text-sm">Còn lại: {product.stock}</span>
                </div>
              </div>
            </div>
          </DraggableSection>
        );

      case "flash-sale":
        return (
          <DraggableSection key={section.id} id={section.id} index={index}>
            <FlashSale className="mb-4" />
          </DraggableSection>
        );

      case "products":
        return (
          <DraggableSection key={section.id} id={section.id} index={index}>
            <div className="px-4 mb-4">
              <div className="grid grid-cols-3 gap-3">
                {section.content.items.map((product: any) => (
                  <ProductCard
                    key={product.id}
                    image={product.image}
                    title={product.title}
                    price={product.price}
                    originalPrice={product.originalPrice}
                    discount={product.discount}
                    rating={product.rating}
                    sold={product.sold}
                  />
                ))}
              </div>
            </div>
          </DraggableSection>
        );

      case "banners":
        return (
          <DraggableSection key={section.id} id={section.id} index={index}>
            <div className="px-4 mb-4 space-y-3">
              {section.content.items.map((banner: any) => (
                <BannerCard
                  key={banner.id}
                  title={banner.title}
                  subtitle={banner.subtitle}
                  image={banner.image}
                  backgroundColor={banner.backgroundColor}
                />
              ))}
            </div>
          </DraggableSection>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-muted/30 ">
      <div className="max-w-6xl mx-auto">
        {/* <DeviceSelector
          selectedPlatform={selectedPlatform}
          selectedModel={selectedModel}
          onPlatformChange={handlePlatformChange}
          onModelChange={setSelectedModel}
        /> */}

        <div className="flex justify-center">
          <MobileScreen deviceModel={getCurrentDevice()}>
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="mobile-app">
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className="h-full overflow-y-auto pb-16 scrollbar-hide"
                    style={{
                      scrollbarWidth: "none",
                      msOverflowStyle: "none",
                    }}
                  >
                    {/* Scrollable Content */}
                    <div className="pt-4">
                      {sections.map((section, index) =>
                        renderSection(section, index)
                      )}
                      {provided.placeholder}
                    </div>
                  </div>
                )}
              </Droppable>
            </DragDropContext>

            {/* Bottom Navigation */}
            <BottomNavigation
              items={bottomNavItems}
              onItemClick={(id) => {
                const navItem = bottomNavItems.find((item) => item.id === id);
                if (navItem && navItem.href) {
                  handleNavigation(navItem.href);
                }
              }}
            />
          </MobileScreen>
        </div>
      </div>
    </div>
  );
};

export default MobileApp;
