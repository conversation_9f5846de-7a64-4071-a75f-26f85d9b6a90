import { useState, useEffect } from "react";
import { DragDropContext, Droppable, DropResult } from "react-beautiful-dnd";
import { MobileScreen } from "@/components/mobile/MobileScreen";
import { DEVICE_MODELS, DeviceModel } from "@/components/mobile/DeviceSelector";
import { CategoryIcon } from "@/components/mobile/CategoryIcon";
import { FlashSale } from "@/components/mobile/FlashSale";
import { ProductCard } from "@/components/mobile/ProductCard";
import { BannerCard } from "@/components/mobile/BannerCard";
import { DraggableSection } from "@/components/mobile/DraggableSection";
import { BottomNavigation } from "@/components/mobile/BottomNavigation";
import { useToast } from "@/hooks/use-toast";
import appData from "./data.json";
import {
  Home,
  LayoutGrid,
  Search,
  User,
  ShoppingCart,
  FileText,
  ShoppingBag,
  Filter,
  MessageCircle,
  Settings,
  Gift,
  Package,
  Smartphone,
  Baby,
  Gamepad2,
  Palette,
  Apple,
  Car,
  Shirt,
  <PERSON>,
  Monitor,
  Archive,
} from "lucide-react";

interface AppSection {
  id: string;
  type: string;
  content: any;
  settings?: any;
  templateVersion?: number;
}

interface PageData {
  name: string;
  icon: string;
  href: string;
  layouts: any;
}

const MobileApp = () => {
  const { toast } = useToast();

  const [selectedPlatform, setSelectedPlatform] = useState<
    "iphone" | "android"
  >("iphone");
  const [selectedModel, setSelectedModel] = useState<string>("iphone-15-pro");
  const [sections, setSections] = useState<AppSection[]>([]);
  const [bottomNavItems, setBottomNavItems] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState<string>("/home");

  const getCurrentDevice = (): DeviceModel => {
    const models = DEVICE_MODELS[selectedPlatform];
    return models.find((model) => model.id === selectedModel) || models[0];
  };

  // Dynamic template processor
  const processLayoutTemplate = (
    layoutKey: string,
    layoutData: any
  ): AppSection | null => {
    // Extract template type and version from layoutKey (e.g., "Header1", "Category2", "Menu5")
    const templateMatch = layoutKey.match(/^([A-Za-z]+)(\d+)(_\d+)?$/);
    if (!templateMatch) return null;

    const [, templateType, templateVersion] = templateMatch;
    const templateId = `${templateType.toLowerCase()}-${layoutKey}`;

    switch (templateType.toLowerCase()) {
      case "header":
        return {
          id: templateId,
          type: "header",
          content: layoutData.settings,
          settings: layoutData.settings,
          templateVersion: parseInt(templateVersion),
        };

      case "category":
        return {
          id: templateId,
          type: "categories",
          content: {
            items:
              layoutData.data?.map((item: any) => ({
                icon: "smartphone",
                title: item.name,
                image: item.image_url,
                url: item.url,
              })) || [],
          },
          settings: layoutData.settings,
          templateVersion: parseInt(templateVersion),
        };

      case "pagecategory":
        if (layoutData.defaultData && layoutData.defaultData.length > 0) {
          return {
            id: templateId,
            type: "category-list",
            content: {
              items: layoutData.defaultData.map((item: any) => ({
                id: item.xid,
                name: item.name,
                image: item.image_url,
                banner: item.banner_image_url,
                slug: item.slug,
                childs: item.childs || [],
              })),
            },
            settings: layoutData.settings,
            templateVersion: parseInt(templateVersion),
          };
        }
        break;

      case "pagesearch":
        return {
          id: templateId,
          type: "search",
          content: {},
          settings: layoutData.settings,
          templateVersion: parseInt(templateVersion),
        };

      case "pageprofile":
        return {
          id: templateId,
          type: "profile",
          content: {},
          settings: layoutData.settings,
          templateVersion: parseInt(templateVersion),
        };

      case "pageproductdetail":
        if (layoutData.data) {
          const productData = layoutData.data;
          return {
            id: templateId,
            type: "product-detail",
            content: {
              product: {
                id: productData.xid,
                name: productData.name,
                image: productData.image_url,
                price: productData.details?.sales_price,
                originalPrice: productData.details?.mrp,
                description: productData.description,
                category: productData.category?.name,
                brand: productData.brand?.name,
                stock: productData.details?.current_stock,
                rating: productData.ratings || [],
                sold: productData.total_sold,
              },
            },
            settings: layoutData.settings,
            templateVersion: parseInt(templateVersion),
          };
        }
        break;

      case "pagecart":
        return {
          id: templateId,
          type: "cart",
          content: {},
          settings: layoutData.settings,
          templateVersion: parseInt(templateVersion),
        };

      case "pagenewscategory":
        return {
          id: templateId,
          type: "news-category",
          content: {},
          settings: layoutData.settings,
          templateVersion: parseInt(templateVersion),
        };

      default:
        console.warn(`Unknown template type: ${templateType}`);
        return null;
    }

    return null;
  };

  // Load data from JSON file based on current page
  const loadPageData = (pagePath: string) => {
    const pageData = appData.page[pagePath];
    const loadedSections: AppSection[] = [];

    if (pageData && pageData.layouts) {
      Object.entries(pageData.layouts).forEach(
        ([layoutKey, layoutData]: [string, any]) => {
          if (layoutData.visible) {
            const section = processLayoutTemplate(layoutKey, layoutData);
            if (section) {
              loadedSections.push(section);
            }
          }
        }
      );
    }

    setSections(loadedSections);
  };

  useEffect(() => {
    loadPageData(currentPage);

    // Load bottom navigation dynamically
    const loadBottomNavigation = () => {
      // Find the first visible menu in menuBottom
      const menuBottomData = appData.menuBottom;
      if (!menuBottomData) return;

      const availableMenus = Object.keys(menuBottomData);
      console.log("🔍 Available menu templates:", availableMenus);

      const activeMenu = availableMenus.find((menuKey) => {
        const menuData = menuBottomData[menuKey];
        return menuData && menuData.visible;
      });

      if (activeMenu) {
        console.log("✅ Using menu template:", activeMenu);
        const menuData = menuBottomData[activeMenu];
        const navItems = menuData.layouts.map((item: any, index: number) => ({
          id: item.href.replace("/", "") || `nav-${index}`,
          icon: getLucideIconIdentifier(item.icon),
          label: item.label,
          active: item.href === currentPage,
          href: item.href,
          menuTemplate: activeMenu, // Store which menu template is being used
        }));
        setBottomNavItems(navItems);
      }
    };

    loadBottomNavigation();

    // Log loaded templates for debugging
    console.log(`📱 Loading page: ${currentPage}`);
    console.log(`📊 Total sections loaded: ${sections.length}`);
    sections.forEach((section) => {
      console.log(
        `  - ${section.type} (${section.id}) - Template v${
          section.templateVersion || 1
        }`
      );
    });
  }, [currentPage, sections]);

  // Helper function to convert icon names to string identifiers
  const getLucideIconIdentifier = (iconName: string): string => {
    const iconMap: { [key: string]: string } = {
      Home: "home",
      Category: "grid",
      Paper: "search", // FileText mapped to search for news
      User: "user",
      Search: "search",
      ShoppingCart: "cart",
      LayoutGrid: "grid",
      Cuboid: "shopping",
    };
    return iconMap[iconName] || "shopping";
  };

  // Helper function to convert emoji to Lucide icons
  const getIconFromEmoji = (emoji: string) => {
    const emojiToIconMap: { [key: string]: any } = {
      "🍼": Milk,
      "👶": Baby,
      "🧸": Gamepad2,
      "💄": Palette,
      "🍎": Apple,
      "🚗": Car,
      "👕": Shirt,
      "📱": Smartphone,
      "📦": Archive,
      "🛒": ShoppingCart,
      "🔍": Search,
      "👤": User,
      "🏠": Home,
      "📂": LayoutGrid,
      "📰": FileText,
    };
    return emojiToIconMap[emoji] || ShoppingBag;
  };

  // Handle navigation
  const handleNavigation = (href: string) => {
    setCurrentPage(href);
    // Update active state for bottom nav
    setBottomNavItems((prev) =>
      prev.map((item) => ({
        ...item,
        active: item.href === href,
      }))
    );
  };

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const items = Array.from(sections);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    setSections(items);
  };

  // Dynamic section renderer based on template version
  const renderSection = (section: AppSection, index: number) => {
    const templateVersion = section.templateVersion || 1;

    switch (section.type) {
      case "header":
        return renderHeaderTemplate(section, index, templateVersion);
      case "categories":
        return renderCategoryTemplate(section, index, templateVersion);
      case "category-list":
        return renderCategoryListTemplate(section, index, templateVersion);
      case "search":
        return renderSearchTemplate(section, index, templateVersion);
      case "profile":
        return renderProfileTemplate(section, index, templateVersion);
      case "cart":
        return renderCartTemplate(section, index, templateVersion);
      case "product-detail":
        return renderProductDetailTemplate(section, index, templateVersion);
      case "news-category":
        return renderNewsCategoryTemplate(section, index, templateVersion);
      case "flash-sale":
        return renderFlashSaleTemplate(section, index, templateVersion);
      case "products":
        return renderProductsTemplate(section, index, templateVersion);
      case "banners":
        return renderBannersTemplate(section, index, templateVersion);
      default:
        console.warn(`Unknown section type: ${section.type}`);
        return null;
    }
  };

  // Header template renderer with version support
  const renderHeaderTemplate = (
    section: AppSection,
    index: number,
    version: number
  ) => {
    const content = section.content;
    const settings = section.settings;

    return (
      <DraggableSection key={section.id} id={section.id} index={index}>
        <div
          className="px-4 py-2"
          style={{
            background: settings?.background || "white",
            margin: settings?.margin || "0px",
            padding: settings?.padding || "8px",
            borderRadius: settings?.borderRadius || "0px",
          }}
        >
          <div className="flex items-center justify-between">
            <div>
              {content.visibleTitle && (
                <h1
                  className="text-lg font-semibold"
                  style={{
                    color: content.colorTitle,
                    fontSize: `${content.fontSizeTitle || 16}px`,
                  }}
                >
                  {content.title}
                </h1>
              )}
              {content.visibleSubTitle && (
                <p
                  className="text-sm"
                  style={{
                    color: content.colorSubTitle,
                    fontSize: `${content.fontSizeSubTitle || 12}px`,
                  }}
                >
                  {content.subTitle}
                </p>
              )}
            </div>
            <div className="flex items-center gap-2">
              {content.visibleCartIcon && <ShoppingCart size={20} />}
              {content.visibleFilterIcon && <Filter size={20} />}
              {content.visibleMessageIcon && <MessageCircle size={20} />}
              {content.visibleLogo && <span className="text-lg">🏢</span>}
            </div>
          </div>
          {content.visibleSearchBar && (
            <div className="mt-2">
              <input
                type="text"
                placeholder={content.placeholderSearchBar}
                className="w-full px-3 py-2 border rounded-lg"
              />
            </div>
          )}
        </div>
      </DraggableSection>
    );
  };

  // Category template renderer with version support
  const renderCategoryTemplate = (
    section: AppSection,
    index: number,
    version: number
  ) => {
    const colCount = section.settings?.col || 4;
    const gridCols =
      colCount === 4
        ? "grid-cols-4"
        : colCount === 5
        ? "grid-cols-5"
        : "grid-cols-4";

    return (
      <DraggableSection key={section.id} id={section.id} index={index}>
        <div className={`grid ${gridCols} gap-2 px-4 mb-4`}>
          {section.content.items.map((item: any, idx: number) => (
            <CategoryIcon
              key={idx}
              icon={item.icon || "shopping"}
              title={item.title}
              image={item.image}
              onClick={() => item.url && handleNavigation(item.url)}
            />
          ))}

          {/* Demo section with Lucide icons for common categories */}
          {section.id === "category-Category2" && (
            <>
              <CategoryIcon icon="milk" title="Sữa" />
              <CategoryIcon icon="baby" title="Bỉm tã" />
              <CategoryIcon icon="gamepad" title="Đồ chơi" />
              <CategoryIcon icon="palette" title="Mỹ phẩm" />
              <CategoryIcon icon="apple" title="Thực phẩm" />
              <CategoryIcon icon="car" title="Xe đẩy" />
              <CategoryIcon icon="shirt" title="Quần áo" />
              <CategoryIcon icon="milk" title="Bình sữa" />
              <CategoryIcon icon="smartphone" title="Thiết bị" />
              <CategoryIcon icon="archive" title="Tất cả" />
            </>
          )}
        </div>
      </DraggableSection>
    );
  };

  // Other template renderers (simplified for now)
  const renderCategoryListTemplate = (
    section: AppSection,
    index: number,
    version: number
  ) => {
    return (
      <DraggableSection key={section.id} id={section.id} index={index}>
        <div className="px-4 mb-4">
          <div className="grid grid-cols-2 gap-3">
            {section.content.items.map((category: any) => (
              <div
                key={category.id}
                className="bg-white rounded-lg p-3 shadow-sm border"
              >
                <img
                  src={category.image}
                  alt={category.name}
                  className="w-full h-20 object-cover rounded mb-2"
                />
                <h3 className="font-medium text-sm">{category.name}</h3>
                {category.childs && category.childs.length > 0 && (
                  <p className="text-xs text-gray-500">
                    {category.childs.length} sản phẩm
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>
      </DraggableSection>
    );
  };

  // Template renderer functions for different component types
  const renderSearchTemplate = (
    section: AppSection,
    index: number,
    version: number
  ) => (
    <DraggableSection key={section.id} id={section.id} index={index}>
      <div className="px-4 mb-4">
        <div className="bg-white rounded-lg p-4">
          <input
            type="text"
            placeholder="Tìm kiếm sản phẩm..."
            className="w-full px-3 py-2 border rounded-lg mb-4"
          />
          <div className="text-center text-gray-500">
            <p>Nhập từ khóa để tìm kiếm sản phẩm</p>
          </div>
        </div>
      </div>
    </DraggableSection>
  );

  const renderProfileTemplate = (
    section: AppSection,
    index: number,
    version: number
  ) => (
    <DraggableSection key={section.id} id={section.id} index={index}>
      <div className="px-4 mb-4">
        <div className="bg-white rounded-lg p-4">
          <div className="flex items-center mb-4">
            <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
              <User size={32} className="text-gray-600" />
            </div>
            <div className="ml-3">
              <h3 className="font-semibold">Người dùng</h3>
              <p className="text-sm text-gray-500">
                Chào mừng bạn đến với ứng dụng
              </p>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between py-2">
              <span>Đơn hàng của tôi</span>
              <Package size={20} className="text-gray-600" />
            </div>
            <div className="flex items-center justify-between py-2">
              <span>Ưu đãi của tôi</span>
              <Gift size={20} className="text-gray-600" />
            </div>
            <div className="flex items-center justify-between py-2">
              <span>Cài đặt</span>
              <Settings size={20} className="text-gray-600" />
            </div>
          </div>
        </div>
      </div>
    </DraggableSection>
  );

  const renderCartTemplate = (
    section: AppSection,
    index: number,
    version: number
  ) => (
    <DraggableSection key={section.id} id={section.id} index={index}>
      <div className="px-4 mb-4">
        <div className="bg-white rounded-lg p-4">
          <div className="text-center text-gray-500">
            <ShoppingCart size={48} className="mx-auto text-gray-400" />
            <p className="mt-2">Giỏ hàng trống</p>
            <p className="text-sm">Hãy thêm sản phẩm vào giỏ hàng</p>
          </div>
        </div>
      </div>
    </DraggableSection>
  );

  const renderProductDetailTemplate = (
    section: AppSection,
    index: number,
    version: number
  ) => {
    const product = section.content.product;
    return (
      <DraggableSection key={section.id} id={section.id} index={index}>
        <div className="px-4 mb-4">
          <div className="bg-white rounded-lg p-4">
            <img
              src={product.image}
              alt={product.name}
              className="w-full h-48 object-cover rounded mb-3"
            />
            <h3 className="font-semibold text-lg mb-2">{product.name}</h3>
            <div className="flex items-center gap-2 mb-2">
              <span className="text-lg font-bold text-red-500">
                {product.price?.toLocaleString()}đ
              </span>
              {product.originalPrice && (
                <span className="text-sm text-gray-500 line-through">
                  {product.originalPrice?.toLocaleString()}đ
                </span>
              )}
            </div>
            <p className="text-sm text-gray-600 mb-3">{product.category}</p>
            <div className="flex items-center justify-between">
              <span className="text-sm">Đã bán: {product.sold}</span>
              <span className="text-sm">Còn lại: {product.stock}</span>
            </div>
          </div>
        </div>
      </DraggableSection>
    );
  };

  const renderNewsCategoryTemplate = (
    section: AppSection,
    index: number,
    version: number
  ) => (
    <DraggableSection key={section.id} id={section.id} index={index}>
      <div className="px-4 mb-4">
        <div className="bg-white rounded-lg p-4">
          <h3 className="font-semibold mb-3">Tin tức</h3>
          <div className="text-center text-gray-500">
            <FileText size={48} className="mx-auto text-gray-400" />
            <p className="mt-2">Chưa có tin tức</p>
          </div>
        </div>
      </div>
    </DraggableSection>
  );

  const renderFlashSaleTemplate = (
    section: AppSection,
    index: number,
    version: number
  ) => (
    <DraggableSection key={section.id} id={section.id} index={index}>
      <FlashSale className="mb-4" />
    </DraggableSection>
  );

  const renderProductsTemplate = (
    section: AppSection,
    index: number,
    version: number
  ) => (
    <DraggableSection key={section.id} id={section.id} index={index}>
      <div className="px-4 mb-4">
        <div className="grid grid-cols-3 gap-3">
          {section.content.items.map((product: any) => (
            <ProductCard
              key={product.id}
              image={product.image}
              title={product.title}
              price={product.price}
              originalPrice={product.originalPrice}
              discount={product.discount}
              rating={product.rating}
              sold={product.sold}
            />
          ))}
        </div>
      </div>
    </DraggableSection>
  );

  const renderBannersTemplate = (
    section: AppSection,
    index: number,
    version: number
  ) => (
    <DraggableSection key={section.id} id={section.id} index={index}>
      <div className="px-4 mb-4 space-y-3">
        {section.content.items.map((banner: any) => (
          <BannerCard
            key={banner.id}
            title={banner.title}
            subtitle={banner.subtitle}
            image={banner.image}
            backgroundColor={banner.backgroundColor}
          />
        ))}
      </div>
    </DraggableSection>
  );

  return (
    <div className="min-h-screen bg-muted/30 ">
      <div className="max-w-6xl mx-auto">
        {/* <DeviceSelector
          selectedPlatform={selectedPlatform}
          selectedModel={selectedModel}
          onPlatformChange={handlePlatformChange}
          onModelChange={setSelectedModel}
        /> */}

        <div className="flex justify-center">
          <MobileScreen deviceModel={getCurrentDevice()}>
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="mobile-app">
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className="h-full overflow-y-auto pb-16 scrollbar-hide"
                    style={{
                      scrollbarWidth: "none",
                      msOverflowStyle: "none",
                    }}
                  >
                    {/* Scrollable Content */}
                    <div className="pt-4">
                      {sections.map((section, index) =>
                        renderSection(section, index)
                      )}
                      {provided.placeholder}
                    </div>
                  </div>
                )}
              </Droppable>
            </DragDropContext>

            {/* Bottom Navigation */}
            <BottomNavigation
              items={bottomNavItems}
              onItemClick={(id) => {
                const navItem = bottomNavItems.find((item) => item.id === id);
                if (navItem && navItem.href) {
                  handleNavigation(navItem.href);
                }
              }}
            />
          </MobileScreen>
        </div>
      </div>
    </div>
  );
};

export default MobileApp;
